<nz-layout class="h-screen">
  <nz-header class="bg-white! border-b border-gray-200 px-0!">
    <div class="h-[60px] bg-primary-200 flex items-center text-white">
      <img src="assets/wiWistron-Logo.wine.white.svg" width="210" class="h-[59px]" />
      <!-- Logo -->
      <div class="font-bold text-3xl cursor-pointer">eLegal Portal</div>

      <div class="flex-1 flex justify-between px-3">
        <div></div>
        <div class="flex items-center">
          <span
            class="icon-[material-symbols--language] text-2xl cursor-pointer"
            nz-dropdown
            [nzDropdownMenu]="menu"
          ></span>
        </div>
      </div>
    </div>

    <!-- header-line -->
    <div class="header-line"></div>
  </nz-header>
  <nz-layout>
    <nz-sider></nz-sider>
    <nz-content class="overflow-y-auto">
      <router-outlet />
    </nz-content>
  </nz-layout>
</nz-layout>

<nz-dropdown-menu #menu="nzDropdownMenu">
  <ul nz-menu nzSelectable>
    @for (item of languages$; track item.key) {
    <li
      nz-menu-item
      [nzSelected]="(currentLanguage$ | async) === item.key"
      (click)="changeLanguage(item.key)"
    >
      {{ item.text }}
    </li>
    }
  </ul>
</nz-dropdown-menu>
