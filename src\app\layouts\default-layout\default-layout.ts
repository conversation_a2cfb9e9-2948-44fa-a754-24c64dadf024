import { Component, inject } from '@angular/core';
import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { LanguageConfigService } from '../../services/LanguageConfigService';
import { AsyncPipe } from '@angular/common';


@Component({
  selector: 'app-default-layout',
  imports: [RouterOutlet, RouterLink, RouterLinkActive, NzLayoutModule, NzDropDownModule, AsyncPipe],
  templateUrl: './default-layout.html',
  styleUrl: './default-layout.css'
})
export class DefaultLayout {
  private languageService = inject(LanguageConfigService);

  // 使用 LanguageService 获取语言列表
  languages$ = this.languageService.getAvailableLanguages();

  // 监听当前语言变化
  currentLanguage$ = this.languageService.getCurrentLanguage();

  /**
   * 切换语言
   * @param languageKey 语言标识符
   */
  changeLanguage(languageKey: string): void {
    this.languageService.setLanguage(languageKey);
  }
}
