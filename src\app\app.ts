import { Component, signal, OnInit, OnDestroy } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, filter } from 'rxjs';

import { DefaultLayout } from './layouts/default-layout/default-layout';
import { BlankLayout } from './layouts/blank-layout/blank-layout';

@Component({
  selector: 'app-root',
  imports: [CommonModule, DefaultLayout, BlankLayout],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit, OnDestroy {
  protected readonly title = signal('Legal');
  protected readonly currentLayout = signal<string>('default');

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    // 监听路由变化，获取当前路由的布局信息
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.updateLayout();
      });

    // 初始化时也要设置布局
    this.updateLayout();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateLayout() {
    let route = this.activatedRoute;

    // 找到最深层的激活路由
    while (route.firstChild) {
      route = route.firstChild;
    }

    // 获取路由数据中的布局信息，默认为 'default'
    const layout = route.snapshot.data['layout'] || 'default';
    this.currentLayout.set(layout);
  }
}
