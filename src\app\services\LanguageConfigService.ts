import { inject, Injectable } from "@angular/core";
import { NzI18nService, en_US, zh_CN, zh_TW } from "ng-zorro-antd/i18n";
import { BehaviorSubject, Observable, from, of } from "rxjs";
import { catchError, map, switchMap, tap } from "rxjs/operators";

export interface Language {
  key: string;
  text: string;
  icon?: string;
  i18n?: any;
  // 语言包加载函数（可选，用于动态加载）
  loadI18n?: () => Promise<any>;
}

export interface SystemConfig {
  currentLanguage: string;
  availableLanguages: Language[];
}

@Injectable({
  providedIn: 'root'
})
export class LanguageConfigService {
  private i18n = inject(NzI18nService);
  private readonly LANGUAGE_STORAGE_KEY = 'system_language';

  // 默认语言列表
  private availableLanguages: Language[] = [
    { key: 'en-US', text: 'English', i18n: en_US },
    { key: 'zh-CN', text: '中文(简体)', i18n: zh_CN },
    { key: 'zh-TW', text: '中文(繁體)', i18n: zh_TW },
  ];

  // 当前语言状态管理
  private currentLanguage$ = new BehaviorSubject<string>('zh-CN');

  constructor() {
    this.initializeCurrentLanguage();
  }

  /**
   * 获取所有可用的语言列表
   */
  getAvailableLanguages(): Language[] {
    return [...this.availableLanguages];
  }

  /**
   * 获取当前语言的Observable
   */
  getCurrentLanguage(): Observable<string> {
    return this.currentLanguage$.asObservable();
  }

  // 获取当前语言的同步快照
  getCurrentLanguageSnapshot(): string {
    return this.currentLanguage$.getValue();
  }

  /**
   * 设置当前语言
   * @param languageKey 语言标识符
   */
  setLanguage(languageKey: string): void {
    // 验证语言是否有效
    if (this.isValidLanguage(languageKey)) {
      this.currentLanguage$.next(languageKey);
      this.saveLanguageToStorage(languageKey);
      this.loadAndSetLocale(languageKey).subscribe({
        next: () => console.log(`语言切换成功: ${languageKey}`),
        error: (error) => console.error(`语言切换失败: ${languageKey}`, error)
      });
    } else {
      console.warn(`无效的语言标识符: ${languageKey}`);
    }
  }

  /**
   * 异步加载并设置语言包
   * @param languageKey 语言标识符
   */
  private loadAndSetLocale(languageKey: string): Observable<void> {
    const language = this.availableLanguages.find(lang => lang.key === languageKey);

    if (!language) {
      return of(undefined);
    }

    // 如果已经有 i18n 包，直接设置
    if (language.i18n) {
      this.i18n.setLocale(language.i18n);
      return of(undefined);
    }

    // 如果有加载函数，动态加载语言包
    if (language.loadI18n) {
      return from(language.loadI18n()).pipe(
        tap((i18nPackage) => {
          // 缓存加载的语言包
          language.i18n = i18nPackage;
          this.i18n.setLocale(i18nPackage);
        }),
        map(() => undefined),
        catchError(error => {
          console.error(`加载语言包失败: ${languageKey}`, error);
          // 回退到默认语言
          this.i18n.setLocale(zh_CN);
          return of(undefined);
        })
      );
    }

    // 如果没有语言包，使用默认中文
    this.i18n.setLocale(zh_CN);
    return of(undefined);
  }

  /**
   * 根据语言标识符获取显示文本
   * @param languageKey 语言标识符
   */
  getLanguageText(languageKey: string): string {
    const language = this.availableLanguages.find(lang => lang.key === languageKey);
    return language ? language.text : languageKey;
  }

  /**
   * 初始化当前语言
   */
  private initializeCurrentLanguage(): void {
    const savedLanguage = this.loadLanguageFromStorage();
    const browserLanguage = navigator.language;

    // 优先级: 保存的语言 > 浏览器语言 > 默认语言
    let targetLanguage = 'zh-CN';

    if (savedLanguage && this.isValidLanguage(savedLanguage)) {
      targetLanguage = savedLanguage;
    } else if (browserLanguage && this.isValidLanguage(browserLanguage)) {
      targetLanguage = browserLanguage;
    } else if (browserLanguage) {
      // 尝试匹配浏览器语言的前缀
      const langPrefix = browserLanguage.split('-')[0];
      const matchedLanguage = this.availableLanguages.find(lang =>
        lang.key.startsWith(langPrefix)
      );
      if (matchedLanguage) {
        targetLanguage = matchedLanguage.key;
      }
    }

    this.currentLanguage$.next(targetLanguage);
    // 异步加载语言包
    this.loadAndSetLocale(targetLanguage).subscribe();
  }

  /**
   * 验证语言标识符是否有效
   * @param languageKey 语言标识符
   */
  private isValidLanguage(languageKey: string): boolean {
    return this.availableLanguages.some(lang => lang.key === languageKey);
  }

  /**
   * 保存语言到本地存储
   * @param languageKey 语言标识符
   */
  private saveLanguageToStorage(languageKey: string): void {
    try {
      localStorage.setItem(this.LANGUAGE_STORAGE_KEY, languageKey);
    } catch (error) {
      console.error('保存语言到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储加载语言
   */
  private loadLanguageFromStorage(): string | null {
    try {
      return localStorage.getItem(this.LANGUAGE_STORAGE_KEY);
    } catch (error) {
      console.error('从本地存储加载语言失败:', error);
      return null;
    }
  }

  /**
   * 获取当前语言包（用于应用配置）
   */
  getCurrentLocale(): any {
    const currentLanguage = this.currentLanguage$.getValue();
    const language = this.availableLanguages.find(lang => lang.key === currentLanguage);
    return language?.i18n || zh_CN;
  }

  /**
   * 获取语言包 Observable（用于响应式配置）
   */
  getLocaleObservable(): Observable<any> {
    return this.currentLanguage$.pipe(
      switchMap(languageKey => {
        const language = this.availableLanguages.find(lang => lang.key === languageKey);
        if (language?.i18n) {
          return of(language.i18n);
        } else if (language?.loadI18n) {
          return from(language.loadI18n()).pipe(
            tap(i18nPackage => {
              language.i18n = i18nPackage;
            }),
            catchError(() => of(zh_CN))
          );
        }
        return of(zh_CN);
      })
    );
  }
}
