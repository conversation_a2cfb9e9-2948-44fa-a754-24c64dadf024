<!--
  Angular 动态布局系统
  =========================================

  这个模板展示了如何根据路由数据动态切换布局组件。

  核心概念：
  1. 根据当前路由的 data.layout 属性动态选择布局组件
  2. 每个布局组件内部包含 <router-outlet>，用于渲染具体的页面组件
  3. 支持多种布局：default（默认布局）、blank（空白布局）等

  工作流程：
  1. 用户导航到某个路由
  2. App 组件监听路由变化事件
  3. 从当前激活路由的 data 属性中获取 layout 信息
  4. 根据 layout 值动态渲染对应的布局组件
  5. 布局组件内的 <router-outlet> 渲染具体的页面组件
-->

<!-- 根据当前路由的布局配置动态渲染布局组件 -->
@switch (currentLayout()) { @case ('blank') {
<app-blank-layout></app-blank-layout>
} @default {
<app-default-layout></app-default-layout>
} }
