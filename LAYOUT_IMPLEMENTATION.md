# Angular 动态布局系统实现

## 概述

本实现通过 Angular 路由的 `data` 属性来指定每个路由应该使用的布局组件，实现了动态布局切换功能。

## 实现的功能

### 1. 路由配置 (`src/app/app.routes.ts`)

```typescript
export const routes: Routes = [
  {
    path: 'login',
    component: LoginComponent,
    data: { layout: 'blank' }  // 使用空白布局
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    data: { layout: 'default' }  // 使用默认布局
  },
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  }
];
```

### 2. 主应用组件 (`src/app/app.ts`)

- 监听路由变化事件
- 从当前激活路由的 `data.layout` 属性获取布局信息
- 使用 signal 来管理当前布局状态
- 支持默认布局回退机制

### 3. 布局组件

#### 默认布局 (`src/app/layouts/default-layout/`)
- 包含头部、主内容区域和底部
- 头部包含导航菜单
- 主内容区域包含 `<router-outlet>`
- 适用于需要完整页面结构的路由

#### 空白布局 (`src/app/layouts/blank-layout/`)
- 最简洁的布局，只包含 `<router-outlet>`
- 适用于登录页面等不需要导航的页面

### 4. 动态布局渲染 (`src/app/app.html`)

使用 Angular 17+ 的新控制流语法：

```html
@switch (currentLayout()) {
  @case ('blank') {
    <app-blank-layout></app-blank-layout>
  }
  @default {
    <app-default-layout></app-default-layout>
  }
}
```

## 使用方法

### 添加新路由

1. 在 `app.routes.ts` 中添加路由配置：
```typescript
{
  path: 'your-page',
  component: YourPageComponent,
  data: { layout: 'default' }  // 或 'blank'
}
```

2. 如果不指定 `layout`，将默认使用 'default' 布局

### 添加新布局

1. 创建新的布局组件
2. 确保布局组件包含 `<router-outlet>`
3. 在主应用组件中导入新布局
4. 在 `app.html` 的 switch 语句中添加新的 case

## 特性

- ✅ 最小化代码更改
- ✅ 保持现有功能兼容性
- ✅ 支持无限扩展新布局
- ✅ 自动路由变化检测
- ✅ 类型安全的布局配置
- ✅ 响应式设计支持

## 测试

1. 访问 `/login` - 应该显示空白布局
2. 访问 `/dashboard` - 应该显示默认布局
3. 在不同路由间导航 - 布局应该自动切换
4. 默认路由重定向到 `/login` 正常工作

## 技术细节

- 使用 Angular 17+ 的 standalone 组件
- 使用新的控制流语法 (@switch)
- 使用 signal 进行状态管理
- 使用 RxJS 监听路由变化
- 支持路由数据的类型安全访问
