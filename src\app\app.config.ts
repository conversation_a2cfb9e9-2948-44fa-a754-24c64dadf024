import { APP_INITIALIZER, ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { zh_CN, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import en from '@angular/common/locales/en';
import ja from '@angular/common/locales/ja';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideHttpClient } from '@angular/common/http';
import { NzConfig, provideNzConfig } from 'ng-zorro-antd/core/config';
import { LanguageConfigService } from './services/LanguageConfigService';


// 注册所有支持的语言环境数据
registerLocaleData(zh);
registerLocaleData(en);

const ngZorroConfig: NzConfig = {
  theme: {
    primaryColor: '#436C85',
    primaryColorHover: '#e4f1f2'
  },
};


export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    // 使用默认中文配置，SystemConfigService会在运行时动态设置
    provideNzI18n(zh_CN),
    provideAnimationsAsync(),
    provideHttpClient(),
    provideNzConfig(ngZorroConfig),
    // 应用初始化器 - 确保语言服务正确初始化
    {
      provide: APP_INITIALIZER,
      useFactory: (configService: LanguageConfigService) => {
        return () => {
          // 初始化时设置正确的语言，通过 setLanguage 方法触发语言包加载
          configService.setLanguage(configService.getCurrentLanguageSnapshot());
          return Promise.resolve();
        };
      },
      deps: [LanguageConfigService],
      multi: true
    }
  ]
};
