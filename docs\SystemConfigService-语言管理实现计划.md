# SystemConfigService 语言管理功能实现计划

## 项目背景

当前项目中，语言配置分散在 [`DefaultLayout`](src/app/layouts/default-layout/default-layout.ts:17) 组件中，需要将其迁移到 [`SystemConfigService`](src/app/services/SystemConfigService.ts:7) 中实现集中化管理。

## 当前状态分析

### 现有语言配置

```typescript
// src/app/layouts/default-layout/default-layout.ts
languages = [
  { key: 'en-US', text: 'English' },
  { key: 'zh-CN', text: '中文(简体)' },
  { key: 'zh-TW', text: '中文(繁體)' },
];
```

### 国际化支持

项目已配置 ng-zorro-antd 中文国际化：

```typescript
// src/app/app.config.ts
import { zh_CN, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';

registerLocaleData(zh);

export const appConfig: ApplicationConfig = {
  providers: [
    provideNzI18n(zh_CN),
    // ...
  ],
};
```

## 设计目标

1. **集中化管理**: 将语言配置从布局组件迁移到 SystemConfigService
2. **状态持久化**: 支持语言选择的本地存储
3. **事件通知**: 提供语言变化的事件机制
4. **可扩展性**: 支持动态语言配置

## 架构设计

```mermaid
graph TB
    subgraph "SystemConfigService"
        A[Language Module] --> B[Language Interface]
        A --> C[Language State Management]
        A --> D[Event System]
    end

    B --> E[Language{key: string, text: string}]
    C --> F[BehaviorSubject<string>]
    C --> G[LocalStorage Persistence]
    D --> H[Observable<string>]

    I[DefaultLayout] --> J[Inject SystemConfigService]
    J --> K[Subscribe to Language Changes]
    J --> L[Call setLanguage() method]
```

## 接口定义

### Language 接口

```typescript
export interface Language {
  key: string; // 语言标识符，如 'zh-CN', 'en-US'
  text: string; // 显示文本，如 '中文(简体)', 'English'
  icon?: string; // 可选的语言图标
}
```

### SystemConfig 接口

```typescript
export interface SystemConfig {
  currentLanguage: string; // 当前选中的语言
  availableLanguages: Language[]; // 可用的语言列表
}
```

## 实现功能

### 1. 核心属性

```typescript
private readonly LANGUAGE_STORAGE_KEY = 'system_language';
private availableLanguages: Language[] = [];
private currentLanguage$ = new BehaviorSubject<string>('zh-CN');
```

### 2. 公共方法

- `getAvailableLanguages(): Language[]` - 获取可用语言列表
- `getCurrentLanguage(): Observable<string>` - 获取当前语言 Observable
- `setLanguage(languageKey: string): void` - 设置当前语言
- `getLanguageText(languageKey: string): string` - 获取语言显示文本

### 3. 私有方法

- `private initializeLanguages(): void` - 初始化语言配置
- `private saveLanguageToStorage(languageKey: string): void` - 保存到本地存储
- `private loadLanguageFromStorage(): string | null` - 从存储加载

## 实现步骤

### 步骤 1: 创建语言接口和类型定义

```typescript
// 在 SystemConfigService 中定义 Language 接口和默认配置
```

### 步骤 2: 实现语言状态管理

```typescript
// 使用 BehaviorSubject 管理当前语言状态
// 实现本地存储持久化
```

### 步骤 3: 迁移默认布局的语言配置

```typescript
// 从 DefaultLayout 移除 languages 数组
// 改为注入 SystemConfigService 获取语言列表
```

### 步骤 4: 更新语言选择器

```typescript
// 修改 default-layout.html 中的语言选择逻辑
// 使用服务方法进行语言切换
```

## 代码结构

### SystemConfigService 结构

```typescript
@Injectable({ providedIn: 'root' })
export class SystemConfigService {
  // 私有属性
  private availableLanguages: Language[] = [];
  private currentLanguage$ = new BehaviorSubject<string>('zh-CN');

  // 构造函数和初始化
  constructor() {
    this.initializeLanguages();
    this.initializeCurrentLanguage();
  }

  // 公共方法
  getAvailableLanguages(): Language[] { ... }
  getCurrentLanguage(): Observable<string> { ... }
  setLanguage(languageKey: string): void { ... }

  // 私有方法
  private initializeLanguages(): void { ... }
  private initializeCurrentLanguage(): void { ... }
}
```

### DefaultLayout 改造

```typescript
export class DefaultLayout {
  // 移除原有的 languages 数组
  languages$ = this.configService.getAvailableLanguages();
  currentLanguage$ = this.configService.getCurrentLanguage();

  constructor(private configService: SystemConfigService) {}

  changeLanguage(languageKey: string): void {
    this.configService.setLanguage(languageKey);
  }
}
```

## 测试计划

### 单元测试

1. **语言列表测试**: 验证 getAvailableLanguages() 返回正确的语言列表
2. **语言切换测试**: 验证 setLanguage() 正确更新当前语言
3. **持久化测试**: 验证语言选择在页面刷新后保持
4. **事件通知测试**: 验证语言变化时 Observable 发出正确值

### 集成测试

1. **布局组件测试**: 验证 DefaultLayout 正确使用 SystemConfigService
2. **语言选择器测试**: 验证下拉菜单正确显示和切换语言

## 扩展性考虑

### 未来功能

1. **动态语言配置**: 支持从 API 加载语言列表
2. **语言包懒加载**: 按需加载翻译文件
3. **多语言图标**: 为每种语言添加标识图标
4. **语言检测**: 自动检测浏览器语言偏好

### 性能优化

1. **内存缓存**: 避免重复创建语言列表
2. **变更检测优化**: 使用 OnPush 策略减少不必要的变更检测

## 风险评估

1. **兼容性风险**: 确保与现有国际化配置兼容
2. **状态同步风险**: 确保多个组件间的语言状态同步
3. **存储冲突风险**: 使用唯一的存储键避免数据冲突

## 实施时间预估

- **设计阶段**: 已完成
- **开发阶段**: 2-3 小时
- **测试阶段**: 1-2 小时
- **部署阶段**: 30 分钟

## 相关文件

- [`SystemConfigService.ts`](src/app/services/SystemConfigService.ts) - 主要实现文件
- [`default-layout.ts`](src/app/layouts/default-layout/default-layout.ts) - 需要改造的布局组件
- [`default-layout.html`](src/app/layouts/default-layout/default-layout.html) - 语言选择器模板
- [`app.config.ts`](src/app/app.config.ts) - 国际化配置

---

_最后更新: 2025-09-19_
